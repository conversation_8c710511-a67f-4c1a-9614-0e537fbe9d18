﻿namespace FileStorageHub.Persistence.Contexts
{
    using Microsoft.EntityFrameworkCore;
    using FileStorageHub.Domain.Contexts;
    public class Temp2DbContext : DbContext, IDbContext
    {
        public Temp2DbContext(DbContextOptions<Temp2DbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.HasDefaultSchema("dbo");
        }
    }
}
