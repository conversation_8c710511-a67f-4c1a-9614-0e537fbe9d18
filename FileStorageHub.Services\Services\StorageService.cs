﻿namespace FileStorageHub.Services.Services
{
    using FileStorageHub.Domain.Repositories;
    using FileStorageHub.Domain.Services;

    public class StorageService : IStorageService
    {
        private readonly IStorageRepository _storageRepository;

        public StorageService(IStorageRepository storageRepository)
        {
            _storageRepository = storageRepository;
        }

        // CV operations
        public async Task<Stream> DownloadCVAsStreamAsync(string cvPath)
        {
            return await _storageRepository.DownloadCVAsStreamAsync(cvPath);
        }

        public async Task UploadCVToBlobStorageAsync(Stream fileStream, string cvPath)
        {
            await _storageRepository.UploadCVToBlobStorageAsync(fileStream, cvPath);
        }

        public async Task<string> GetCVSasTokenAsync(string documentName, string cvLocation)
        {
            return await _storageRepository.GetCVSasTokenAsync(documentName, cvLocation);
        }

        // Image operations
        public async Task<Stream> DownloadImageAsStreamAsync(string imagePath)
        {
            return await _storageRepository.DownloadImageAsStreamAsync(imagePath);
        }

        public async Task UploadImageToBlobStorageAsync(Stream imageStream, string imagePath)
        {
            await _storageRepository.UploadImageToBlobStorageAsync(imageStream, imagePath);
        }

        public async Task DeleteImageFromBlobStorageAsync(string imagePath)
        {
            await _storageRepository.DeleteImageFromBlobStorageAsync(imagePath);
        }

    }
}
