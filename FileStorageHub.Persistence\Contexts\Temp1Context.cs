﻿namespace FileStorageHub.Persistence.Contexts
{
    using Microsoft.EntityFrameworkCore;
    using FileStorageHub.Domain.Contexts;

    public class Temp1Context : DbContext, IDbContext
    {
        public Temp1Context(DbContextOptions<Temp1Context> options) : base(options) { }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.HasDefaultSchema("dbo");

        }
    }
}
