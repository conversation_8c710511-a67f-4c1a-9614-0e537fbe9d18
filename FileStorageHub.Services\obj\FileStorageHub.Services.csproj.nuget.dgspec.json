{"format": 1, "restore": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj": {}}, "projects": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj", "projectName": "FileStorageHub.Domain", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj", "projectName": "FileStorageHub.Persistence", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.20, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.20, )"}, "WindowsAzure.Storage": {"target": "Package", "version": "[9.3.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj", "projectName": "FileStorageHub.Services", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj"}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}