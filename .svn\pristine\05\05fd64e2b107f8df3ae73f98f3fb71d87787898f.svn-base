namespace FileStorageHub.Api.Controllers
{
    using Microsoft.AspNetCore.Mvc;
    using FileStorageHub.Api.Attributes;
    using FileStorageHub.Domain.Services;
    using FileStorageHub.Domain.ViewModels;
    using FileStorageHub.Domain.Models.Views;
    using RecruitmentHub.Domain.Logger;

    [Route("api/[controller]")]
    [ApiController]
    [ApiKeyAuthorisation]
    public class CVController : ControllerBase
    {
        private readonly IStorageService _storageService;
        private readonly ILoggerManager _logger;
        public CVController(IStorageService storageService, ILoggerManager logger)
        {
            _storageService = storageService;
            _logger = logger;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> Upload(IFormFile file)
        {
            try
            {
                _logger.Info($"CV uploaded: {file.FileName}");

                using (var stream = file.OpenReadStream())
                {
                    _logger.Info($"uploading CV to blob");
                    await _storageService.UploadCVToBlobStorageAsync(stream, file.FileName);
                }
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.Error($"Error uploading CV: {ex.Message}");
                return BadRequest(ex.Message);
            }

        }

        [HttpGet("download/{*cvPath}")]
        public async Task<ActionResult<FileResponse>> Download(string cvPath)
        {
            try
            {
                string decodedCVPath = Uri.UnescapeDataString(cvPath);

                var stream = await _storageService.DownloadCVAsStreamAsync(decodedCVPath);

                using (var memoryStream = new MemoryStream())
                {
                    await stream.CopyToAsync(memoryStream);
                    byte[] fileBytes = memoryStream.ToArray();

                    string fileName = Path.GetFileName(decodedCVPath);
                    string fileExtension = Path.GetExtension(decodedCVPath);

                    var result = new FileResponse
                    {
                        FileName = fileName,
                        FileExtension = fileExtension,
                        FileBytes = fileBytes
                    };

                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error downloading CV: {ex.Message}");
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("sas-token/{documentName}/{cvLocation}")]
        public async Task<IActionResult> GetSasToken(string documentName, string cvLocation)
        {
            try
            {
                var sasToken = await _storageService.GetCVSasTokenAsync(documentName, cvLocation);
                return Ok(sasToken);
            }
            catch (Exception ex)
            {
                _logger.Error($"Error getting CV SAS token: {ex.Message}");
                return BadRequest(ex.Message);
            }
        }
    }
}
