namespace FileStorageHub.Domain.Repositories
{
    public interface IStorageRepository
    {
        // CV operations
        Task<Stream> DownloadCVAsStreamAsync(string cvPath);
        Task UploadCVToBlobStorageAsync(Stream fileStream, string cvPath);
        Task<string> GetCVSasTokenAsync(string documentName, string cvLocation);

        // Image operations
        Task<Stream> DownloadImageAsStreamAsync(string imagePath);
        Task UploadImageToBlobStorageAsync(Stream imageStream, string imagePath);
        Task DeleteImageFromBlobStorageAsync(string imagePath);

        // Generic file operations
        Task<Stream> DownloadGenericFileAsStreamAsync(string filePath);
        Task UploadGenericFileToBlobStorageAsync(Stream fileStream, string filePath);
        Task DeleteGenericFileFromBlobStorageAsync(string filePath);
    }
}
