using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using NLog;
using NLog.Web;
using FileStorageHub.Domain.Repositories;
using FileStorageHub.Domain.Services;
using FileStorageHub.Persistence.Contexts;
using FileStorageHub.Persistence.Repositories;
using FileStorageHub.Services.Services;
using FileStorageHub.Domain.Models;
using Microsoft.Extensions.Configuration;
using RecruitmentHub.Domain.Logger;
using LoggerService;

// Early init of NLog to allow startup and exception logging, before host is built
var logger = NLog.LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
logger.Debug("init main");

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Add services to the container.

    // NLog: Setup NLog for Dependency injection
    builder.Logging.ClearProviders();
    builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Trace);
    builder.Host.UseNLog();

    //other classes that need the logger
    // builder.Services.AddTransient<GenericHelper>();

    builder.Services.AddControllers();
    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(c =>
    {
        c.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme()
        {
            In = ParameterLocation.Header,
            Name = "APIKey",
            Type = SecuritySchemeType.ApiKey,
        });
        c.AddSecurityRequirement(new OpenApiSecurityRequirement {  {
                        new OpenApiSecurityScheme { Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "ApiKey" } },
                new string[] { }
                }});
    });
    builder.Services.Configure<AzureStorageSettings>(builder.Configuration.GetSection("AzureStorageSettings"));

    builder.Services.AddSingleton<ILoggerManager, LoggerManager>();

    builder.Services.AddScoped<Temp1Context, Temp1Context>();
    builder.Services.AddScoped<IStorageRepository, StorageRepository>();
    builder.Services.AddScoped<IStorageService, StorageService>();

    builder.Services.AddDbContext<Temp1Context>(options =>
    {
        options.UseSqlServer(builder.Configuration.GetConnectionString("DS_MasterData"),
            builder => builder.EnableRetryOnFailure(maxRetryCount: 3, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null));
    });

    builder.Services.AddDbContext<Temp2DbContext>(options =>
    {
        options.UseSqlServer(builder.Configuration.GetConnectionString("GeoSpatial"),
            builder => builder.EnableRetryOnFailure(maxRetryCount: 3, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null));
    });

    //builder.Services.AddControllersWithViews()
    //    .AddJsonOptions(options =>
    //        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.Preserve);

    var app = builder.Build();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI();
    }

    app.UseHttpsRedirection();

    app.UseAuthorization();

    app.MapControllers();

    app.Run();

}
catch (Exception e)
{
    logger.Error(e, "Stopped program because of exception");
    throw;
}
finally
{
    // Ensure to flush and stop internal timers/threads before application-exit (Avoid segmentation fault on Linux)
    LogManager.Shutdown();
}
