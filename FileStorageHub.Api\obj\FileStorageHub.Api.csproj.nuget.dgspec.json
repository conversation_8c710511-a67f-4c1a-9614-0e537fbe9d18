{"format": 1, "restore": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Api\\FileStorageHub.Api.csproj": {}}, "projects": {"C:\\Dev\\FileStorageHub\\trunk\\BranchedProjects\\Logging\\Logging.csproj": {"restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\BranchedProjects\\Logging\\Logging.csproj", "projectName": "Logging", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\BranchedProjects\\Logging\\Logging.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net35": {"projectReferences": {}}}}, "frameworks": {"net35": {}}}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Api\\FileStorageHub.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Api\\FileStorageHub.Api.csproj", "projectName": "FileStorageHub.Api", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Api\\FileStorageHub.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\FileStorageHub\\trunk\\BranchedProjects\\Logging\\Logging.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\BranchedProjects\\Logging\\Logging.csproj"}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj"}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj"}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj"}, "C:\\Dev\\FileStorageHub\\trunk\\LoggerService\\LoggerService.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\LoggerService\\LoggerService.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.20, )"}, "NLog": {"target": "Package", "version": "[5.2.3, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[5.3.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.2.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj", "projectName": "FileStorageHub.Domain", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj", "projectName": "FileStorageHub.Persistence", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.20, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.20, )"}, "WindowsAzure.Storage": {"target": "Package", "version": "[9.3.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj", "projectName": "FileStorageHub.Services", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\FileStorageHub.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj"}, "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Persistence\\FileStorageHub.Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\FileStorageHub\\trunk\\LoggerService\\LoggerService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\FileStorageHub\\trunk\\LoggerService\\LoggerService.csproj", "projectName": "LoggerService", "projectPath": "C:\\Dev\\FileStorageHub\\trunk\\LoggerService\\LoggerService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\FileStorageHub\\trunk\\LoggerService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj": {"projectPath": "C:\\Dev\\FileStorageHub\\trunk\\FileStorageHub.Domain\\FileStorageHub.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.0, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[4.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}