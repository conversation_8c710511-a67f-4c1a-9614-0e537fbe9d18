namespace FileStorageHub.Domain.Services
{
    public interface IStorageService
    {
        // CV operations
        Task<Stream> DownloadCVAsStreamAsync(string cvPath);
        Task UploadCVToBlobStorageAsync(Stream fileStream, string cvPath);
        Task<string> GetCVSasTokenAsync(string documentName, string cvLocation);

        // Image operations
        Task<Stream> DownloadImageAsStreamAsync(string imagePath);
        Task UploadImageToBlobStorageAsync(Stream imageStream, string imagePath);
        Task DeleteImageFromBlobStorageAsync(string imagePath);

        // File operations
        Task<Stream> DownloadFileAsStreamAsync(string filePath);
        Task UploadFileToBlobStorageAsync(Stream fileStream, string filePath);
        Task DeleteFileFromBlobStorageAsync(string filePath);
    }
}
