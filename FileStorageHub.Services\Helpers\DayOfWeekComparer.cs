﻿namespace FileStorageHub.Services.Helpers
{
    using System;
    using System.Collections.Generic;

    public class DayOfWeekComparer : IComparer<DayOfWeek>
    {
        private readonly DayOfWeek startDay;

        public DayOfWeekComparer(DayOfWeek startDay)
        {
            this.startDay = startDay;
        }

        public int Compare(DayOfWeek x, DayOfWeek y)
        {
            int xValue = (x - startDay + 7) % 7;
            int yValue = (y - startDay + 7) % 7;

            return xValue - yValue;
        }
    }
}
