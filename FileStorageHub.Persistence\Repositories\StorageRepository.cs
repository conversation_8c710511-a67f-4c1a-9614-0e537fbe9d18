namespace FileStorageHub.Persistence.Repositories
{
    using FileStorageHub.Domain.Models;
    using FileStorageHub.Domain.Repositories;
    using Microsoft.Extensions.Options;
    using Microsoft.WindowsAzure.Storage;
    using Microsoft.WindowsAzure.Storage.Blob;

    public class StorageRepository : IStorageRepository
    {
        private readonly AzureStorageSettings _storageSettings;

        public StorageRepository(IOptions<AzureStorageSettings> storageSettings)
        {
            _storageSettings = storageSettings.Value;
        }

        // CV operations
        public async Task<Stream> DownloadCVAsStreamAsync(string cvPath)
        {
            CloudBlockBlob blockBlob = await GetCVBlockBlobAsync(cvPath);
            var memoryStream = new MemoryStream();
            await blockBlob.DownloadToStreamAsync(memoryStream);
            memoryStream.Position = 0;
            return memoryStream;
        }

        public async Task UploadCVToBlobStorageAsync(Stream fileStream, string cvPath)
        {
            CloudBlockBlob blockBlob = await GetCVBlockBlobAsync(cvPath);
            await blockBlob.UploadFromStreamAsync(fileStream);
        }

        public async Task<string> GetCVSasTokenAsync(string documentName, string cvLocation)
        {
            var sasPolicy = new SharedAccessBlobPolicy()
            {
                Permissions = SharedAccessBlobPermissions.Read,
                SharedAccessStartTime = DateTime.Now.AddMinutes(-10),
                SharedAccessExpiryTime = DateTime.Now.AddMinutes(10)
            };

            var container = await GetCVBlobContainerAsync();
            var blob = container.GetBlockBlobReference(documentName);
            var sasToken = blob.GetSharedAccessSignature(sasPolicy);
            return $"{blob.StorageUri.PrimaryUri}{sasToken}";
        }

        // Image operations
        public async Task<Stream> DownloadImageAsStreamAsync(string imagePath)
        {
            CloudBlockBlob blockBlob = await GetImageBlockBlobAsync(imagePath);
            var memoryStream = new MemoryStream();
            await blockBlob.DownloadToStreamAsync(memoryStream);
            memoryStream.Position = 0;
            return memoryStream;
        }

        public async Task UploadImageToBlobStorageAsync(Stream imageStream, string imagePath)
        {
            CloudBlockBlob blockBlob = await GetImageBlockBlobAsync(imagePath);

            blockBlob.Properties.ContentType = "image/jpeg";

            await blockBlob.UploadFromStreamAsync(imageStream);
        }

        public async Task DeleteImageFromBlobStorageAsync(string imagePath)
        {
            CloudBlockBlob blockBlob = await GetImageBlockBlobAsync(imagePath);
            await blockBlob.DeleteIfExistsAsync();
        }

        // File operations
        public async Task<Stream> DownloadFileAsStreamAsync(string filePath)
        {
            CloudBlockBlob blockBlob = await GetFileBlockBlobAsync(filePath);
            var memoryStream = new MemoryStream();
            await blockBlob.DownloadToStreamAsync(memoryStream);
            memoryStream.Position = 0;
            return memoryStream;
        }

        public async Task UploadFileToBlobStorageAsync(Stream fileStream, string filePath)
        {
            CloudBlockBlob blockBlob = await GetFileBlockBlobAsync(filePath);
            await blockBlob.UploadFromStreamAsync(fileStream);
        }

        public async Task DeleteFileFromBlobStorageAsync(string filePath)
        {
            CloudBlockBlob blockBlob = await GetFileBlockBlobAsync(filePath);
            await blockBlob.DeleteIfExistsAsync();
        }

        private async Task<CloudBlockBlob> GetCVBlockBlobAsync(string cvPath)
        {
            CloudBlobContainer blobContainer = await GetCVBlobContainerAsync();
            return blobContainer.GetBlockBlobReference(cvPath);
        }

        private async Task<CloudBlockBlob> GetImageBlockBlobAsync(string imagePath)
        {
            CloudBlobContainer blobContainer = await GetImageBlobContainerAsync();
            return blobContainer.GetBlockBlobReference(imagePath);
        }

        private async Task<CloudBlockBlob> GetFileBlockBlobAsync(string filePath)
        {
            CloudBlobContainer blobContainer = await GetFileBlobContainerAsync();
            return blobContainer.GetBlockBlobReference(filePath);
        }

        private async Task<CloudBlobContainer> GetCVBlobContainerAsync()
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(_storageSettings.StorageConnectionString);
            CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
            CloudBlobContainer blobContainer = blobClient.GetContainerReference(_storageSettings.CVDocumentsBlobContainerName);
            await blobContainer.CreateIfNotExistsAsync();
            return blobContainer;
        }

        private async Task<CloudBlobContainer> GetImageBlobContainerAsync()
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(_storageSettings.StorageConnectionString);
            CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
            CloudBlobContainer blobContainer = blobClient.GetContainerReference(_storageSettings.ImageBlobContainerName);
            await blobContainer.CreateIfNotExistsAsync();
            return blobContainer;
        }

        private async Task<CloudBlobContainer> GetFileBlobContainerAsync()
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(_storageSettings.StorageConnectionString);
            CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
            CloudBlobContainer blobContainer = blobClient.GetContainerReference(_storageSettings.FilesBlobContainerName);
            await blobContainer.CreateIfNotExistsAsync();
            return blobContainer;
        }
    }
}
