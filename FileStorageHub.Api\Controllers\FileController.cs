namespace FileStorageHub.Api.Controllers
{
    using FileStorageHub.Domain.Services;
    using FileStorageHub.Api.Models;
    using Microsoft.AspNetCore.Mvc;
    using RecruitmentHub.Domain.Logger;
    using FileStorageHub.Api.Attributes;

    [Route("api/[controller]")]
    [ApiController]
    [ApiKeyAuthorisation]
    public class FileController : ControllerBase
    {
        private readonly IStorageService _storageService;
        private readonly ILoggerManager _logger;

        public FileController(IStorageService storageService, ILoggerManager logger)
        {
            _storageService = storageService;
            _logger = logger;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> Upload(IFormFile file)
        {
            try
            {
                _logger.Info($"File uploaded: {file.FileName}");

                using (var stream = file.OpenReadStream())
                {
                    _logger.Info($"uploading file to blob");
                    await _storageService.UploadFileToBlobStorageAsync(stream, file.FileName);
                }
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.Error($"Error uploading file: {ex.Message}");
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("download/{*filePath}")]
        public async Task<ActionResult<FileResponse>> Download(string filePath)
        {
            try
            {
                string decodedFilePath = Uri.UnescapeDataString(filePath);

                var stream = await _storageService.DownloadFileAsStreamAsync(decodedFilePath);

                using (var memoryStream = new MemoryStream())
                {
                    await stream.CopyToAsync(memoryStream);
                    byte[] fileBytes = memoryStream.ToArray();

                    string fileName = Path.GetFileName(decodedFilePath);
                    string fileExtension = Path.GetExtension(decodedFilePath);

                    var result = new FileResponse
                    {
                        FileName = fileName,
                        FileExtension = fileExtension,
                        FileBytes = fileBytes
                    };

                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error downloading file: {ex.Message}");
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("delete/{*filePath}")]
        public async Task<IActionResult> Delete(string filePath)
        {
            try
            {
                string decodedFilePath = Uri.UnescapeDataString(filePath);
                await _storageService.DeleteFileFromBlobStorageAsync(decodedFilePath);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.Error($"Error deleting file: {ex.Message}");
                return BadRequest(ex.Message);
            }
        }
    }
}
