{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"LoggerService/1.0.0": {"dependencies": {"FileStorageHub.Domain": "1.0.0", "Microsoft.EntityFrameworkCore.Design": "3.1.0", "NLog.Web.AspNetCore": "4.9.3"}, "runtime": {"LoggerService.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/3.1.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "Microsoft.Bcl.HashCode": "1.1.0", "Microsoft.EntityFrameworkCore.Abstractions": "3.1.0", "Microsoft.EntityFrameworkCore.Analyzers": "3.1.0", "Microsoft.Extensions.Caching.Memory": "3.1.0", "Microsoft.Extensions.DependencyInjection": "3.1.0", "Microsoft.Extensions.Logging": "3.1.0", "System.Collections.Immutable": "1.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "3.1.0.0", "fileVersion": "3.100.19.56505"}}}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.0": {"runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "3.1.0.0", "fileVersion": "3.100.19.56505"}}}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.0": {}, "Microsoft.EntityFrameworkCore.Design/3.1.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "3.1.0.0", "fileVersion": "3.100.19.56505"}}}, "Microsoft.EntityFrameworkCore.Relational/3.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "3.1.0.0", "fileVersion": "3.100.19.56505"}}}, "Microsoft.Extensions.Caching.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Caching.Memory/3.1.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "3.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "3.1.0"}}, "Microsoft.Extensions.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0"}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.0": {}, "Microsoft.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.0", "Microsoft.Extensions.DependencyInjection": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "3.1.0"}}, "Microsoft.Extensions.Logging.Abstractions/3.1.0": {}, "Microsoft.Extensions.Options/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.0", "Microsoft.Extensions.Primitives": "3.1.0"}}, "Microsoft.Extensions.Primitives/3.1.0": {}, "NLog/4.7.2": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.7.2.11786"}}}, "NLog.Extensions.Logging/1.6.4": {"dependencies": {"Microsoft.Extensions.Logging": "3.1.0", "NLog": "4.7.2"}, "runtime": {"lib/netcoreapp3.0/NLog.Extensions.Logging.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.6.4.1366"}}}, "NLog.Web.AspNetCore/4.9.3": {"dependencies": {"NLog.Extensions.Logging": "1.6.4"}, "runtime": {"lib/netcoreapp3.0/NLog.Web.AspNetCore.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.9.3.1751"}}}, "System.Collections.Immutable/1.7.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.Diagnostics.DiagnosticSource/4.7.0": {}, "FileStorageHub.Domain/1.0.0": {"runtime": {"FileStorageHub.Domain.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"LoggerService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Am6l4Vpn3/K32daEqZI+FFr96OlZkgwK2LcT3pZ2zWubR5zTPW3/FkO1Rat9kb7oQOa4rxgl9LJHc5tspCWfg==", "path": "microsoft.bcl.asyncinterfaces/1.1.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BG+K/TBDlmkFUviU8lPvmQ3/nDf9e5MKh1il31gLEToV2kgxgkg+JulhtM0xOph6OU1Iyd5A+3c5FyBpI2xh3A==", "path": "microsoft.entityframeworkcore/3.1.0", "hashPath": "microsoft.entityframeworkcore.3.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Wn8vQUq04aeXKVf7pu6/hQxqQliSyM5TocAJUBRQpDNAZGuu3nWMx1biEtZqhGXa4UTLTIJCOb8YANToC8ooA==", "path": "microsoft.entityframeworkcore.abstractions/3.1.0", "hashPath": "microsoft.entityframeworkcore.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Shqqf6MFaIkcuR6dMEBzvG0LMJMoKKMx+k14nUax98vv33i3AUUGxKF9VfJt7VsjOA3xu+KOdzCW8YJ2KLoRIA==", "path": "microsoft.entityframeworkcore.analyzers/3.1.0", "hashPath": "microsoft.entityframeworkcore.analyzers.3.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-zxyus0fUcAhst5Essf2g+GFgKN7BCITJ004DR4uAkLkn8lLS5MoxNDCBNjCF5lGrNx7a6wBaqQE7tgt7Ss2Hog==", "path": "microsoft.entityframeworkcore.design/3.1.0", "hashPath": "microsoft.entityframeworkcore.design.3.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-eeiqVqzXVuwQsUbqXMyXSEUn/EHB9zmDsr5f/+v6uEt0ir7pgItuIi3I7QV4xvM/s0KbFsqGUOrAFPeRHE3plg==", "path": "microsoft.entityframeworkcore.relational/3.1.0", "hashPath": "microsoft.entityframeworkcore.relational.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-+R7RE<PERSON>+Pks1/ITjDdvey+QJzIG3tIYOtrv4RT40UVVe2Y1Sa8pIjJy3MzPZbyXVgOFN3JHFz1UZH8kz04aa5A==", "path": "microsoft.extensions.caching.abstractions/3.1.0", "hashPath": "microsoft.extensions.caching.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-SWVYYFN8K4bUEZAwVbcdxjApYE7JtbjPXIqsQt/vKE243u0qeDuS7bA5hKVr+k5lo2R+BpITe6Mvqmkus2xDRQ==", "path": "microsoft.extensions.caching.memory/3.1.0", "hashPath": "microsoft.extensions.caching.memory.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lu41BWNmwhKr6LgyQvcYBOge0pPvmiaK8R5UHXX4//wBhonJyWcT2OK1mqYfEM5G7pTf31fPrpIHOT6sN7EGOA==", "path": "microsoft.extensions.configuration/3.1.0", "hashPath": "microsoft.extensions.configuration.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ESz6bVoDQX7sgWdKHF6G9Pq672T8k+19AFb/txDXwdz7MoqaNQj2/in3agm/3qae9V+WvQZH86LLTNVo0it8vQ==", "path": "microsoft.extensions.configuration.abstractions/3.1.0", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9eELDBfNkR7sUtYysFZ1Q7BQ1mYt27DMkups/3vu7xgPyOpMD+iAfrBZFzUXT2iw0fmFb8s1gfNBZS+IgjKdQ==", "path": "microsoft.extensions.configuration.binder/3.1.0", "hashPath": "microsoft.extensions.configuration.binder.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KVkv3aF2MQpmGFRh4xRx2CNbc2sjDFk+lH4ySrjWSOS+XoY1Xc+sJphw3N0iYOpoeCCq8976ceVYDH8sdx2qIQ==", "path": "microsoft.extensions.dependencyinjection/3.1.0", "hashPath": "microsoft.extensions.dependencyinjection.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-44rDtOf1JXXAFpNT2EXMExaDm/4OJ2RXOL9i9lE4bK427nzC7Exphv+beB6IgluyE2GIoo8zezTStMXI7MQ8WA==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-P+8sKQ8L4ooL79sxxqwFPxGGC3aBrUDLB/dZqhs4J0XjTyrkeeyJQ4D4nzJB6OnAhy78HIIgQ/RbD6upOXLynw==", "path": "microsoft.extensions.logging/3.1.0", "hashPath": "microsoft.extensions.logging.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jjo4YXRx6MIpv6DiRxJjSpl+sPP0+5VW0clMEdLyIAz44PPwrDTFrd5PZckIxIXl1kKZ2KK6IL2nkt0+ug2MQg==", "path": "microsoft.extensions.logging.abstractions/3.1.0", "hashPath": "microsoft.extensions.logging.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9b6JHY7TAXrSfZ6EEGf+j8XnqKIiMPErfmaNXhJYSCb+BUW2H4RtzkNJvwLJzwgzqBP0wtTjyA6Uw4BPPdmkMw==", "path": "microsoft.extensions.options/3.1.0", "hashPath": "microsoft.extensions.options.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-LEKAnX7lhUhSoIc2XraCTK3M4IU/LdVUzCe464Sa4+7F4ZJuXHHRzZli2mDbiT4xzAZhgqXbvfnb5+CNDcQFfg==", "path": "microsoft.extensions.primitives/3.1.0", "hashPath": "microsoft.extensions.primitives.3.1.0.nupkg.sha512"}, "NLog/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-tfZNTOYr0TwvJNrXsJsVdZPSgIpYN1rXHnAZAadeaIbz+v18agNCCxgZXnXQqNKNizkogJ68zYTNKavyQFWwrg==", "path": "nlog/4.7.2", "hashPath": "nlog.4.7.2.nupkg.sha512"}, "NLog.Extensions.Logging/1.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-ESOBd/bzxFacwpWTdTXaAaItJz/0Mzibyw65YB0uQT4pf2hXtwHp2626qEiVOmaacIQbJH5wGpBLNcVpbQLpKA==", "path": "nlog.extensions.logging/1.6.4", "hashPath": "nlog.extensions.logging.1.6.4.nupkg.sha512"}, "NLog.Web.AspNetCore/4.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-v3TiGaFXENM3/fVCFJJaien7A44EcGwceySh5rFNkp5RC3PZvsbDjEXoOeqsDz8+i5iuYQDMjTFuD2JReNdj9g==", "path": "nlog.web.aspnetcore/4.9.3", "hashPath": "nlog.web.aspnetcore.4.9.3.nupkg.sha512"}, "System.Collections.Immutable/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw==", "path": "system.collections.immutable/1.7.0", "hashPath": "system.collections.immutable.1.7.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-oJjw3uFuVDJiJNbCD8HB4a2p3NYLdt1fiT5OGsPLw+WTOuG0KpP4OXelMmmVKpClueMsit6xOlzy4wNKQFiBLg==", "path": "system.diagnostics.diagnosticsource/4.7.0", "hashPath": "system.diagnostics.diagnosticsource.4.7.0.nupkg.sha512"}, "FileStorageHub.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}