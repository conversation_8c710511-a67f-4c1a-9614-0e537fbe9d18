namespace FileStorageHub.Domain.Repositories
{
    public interface IBlobStorageRepository
    {
        // CV operations
        Task<Stream> DownloadCVAsStreamAsync(string cvName);
        Task UploadCVToBlobStorageAsync(Stream fileStream, string cvName);
        Task<string> GetCVSasTokenAsync(string documentName, string cvLocation);

        // Image operations
        Task<Stream> DownloadImageAsStreamAsync(string imageName);
        Task UploadImageToBlobStorageAsync(Stream imageStream, string imageName);
        Task DeleteImageFromBlobStorageAsync(string imageName);
    }
}
