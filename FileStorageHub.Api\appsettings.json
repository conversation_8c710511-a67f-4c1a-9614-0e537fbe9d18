{"ConnectionStrings": {"DS_MasterData": "Data Source=myd-clarity-SQL;Initial Catalog=DS_MasterData; Trusted_Connection=True;", "GeoSpatial": "Data Source=10.1.2.115;Initial Catalog=GeoSpatial;User ID=IoMartVacancyApiUser; Password=ttMydentUser-!...!;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AzureStorageSettings": {"StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=cvstorageaccount;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AccountName": "cvstorageaccount", "AccountKey": "****************************************************************************************", "CVDocumentsBlobContainerName": "indeed", "ImageBlobContainerName": "images"}, "APIKey": "11233", "AllowedHosts": "*"}