﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33627.172
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FileStorageHub.Api", "FileStorageHub.Api\FileStorageHub.Api.csproj", "{8983B2A3-6AD1-4123-B11C-E0BB0FB4E56D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FileStorageHub.Domain", "FileStorageHub.Domain\FileStorageHub.Domain.csproj", "{38E5FB81-0234-44F7-9A09-A75AC0D9BD40}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FileStorageHub.Services", "FileStorageHub.Services\FileStorageHub.Services.csproj", "{4DB981F1-981E-47DE-9E55-1449A8D36665}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FileStorageHub.Persistence", "FileStorageHub.Persistence\FileStorageHub.Persistence.csproj", "{51233A08-3491-4DC4-BD23-99374FD988BB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "BranchedProjects", "BranchedProjects", "{7932220C-0DD8-45A2-AF56-AA40D34683C6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Logging", "BranchedProjects\Logging\Logging.csproj", "{6784AAA4-02B8-40E5-94DA-04D0ABA2C492}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LoggerService", "LoggerService\LoggerService.csproj", "{21733ED4-FC4F-4429-B9BD-BAADA1C204D6}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8983B2A3-6AD1-4123-B11C-E0BB0FB4E56D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8983B2A3-6AD1-4123-B11C-E0BB0FB4E56D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8983B2A3-6AD1-4123-B11C-E0BB0FB4E56D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8983B2A3-6AD1-4123-B11C-E0BB0FB4E56D}.Release|Any CPU.Build.0 = Release|Any CPU
		{38E5FB81-0234-44F7-9A09-A75AC0D9BD40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38E5FB81-0234-44F7-9A09-A75AC0D9BD40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38E5FB81-0234-44F7-9A09-A75AC0D9BD40}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38E5FB81-0234-44F7-9A09-A75AC0D9BD40}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DB981F1-981E-47DE-9E55-1449A8D36665}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DB981F1-981E-47DE-9E55-1449A8D36665}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DB981F1-981E-47DE-9E55-1449A8D36665}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DB981F1-981E-47DE-9E55-1449A8D36665}.Release|Any CPU.Build.0 = Release|Any CPU
		{51233A08-3491-4DC4-BD23-99374FD988BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{51233A08-3491-4DC4-BD23-99374FD988BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{51233A08-3491-4DC4-BD23-99374FD988BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{51233A08-3491-4DC4-BD23-99374FD988BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{6784AAA4-02B8-40E5-94DA-04D0ABA2C492}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6784AAA4-02B8-40E5-94DA-04D0ABA2C492}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6784AAA4-02B8-40E5-94DA-04D0ABA2C492}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6784AAA4-02B8-40E5-94DA-04D0ABA2C492}.Release|Any CPU.Build.0 = Release|Any CPU
		{21733ED4-FC4F-4429-B9BD-BAADA1C204D6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{21733ED4-FC4F-4429-B9BD-BAADA1C204D6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{21733ED4-FC4F-4429-B9BD-BAADA1C204D6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{21733ED4-FC4F-4429-B9BD-BAADA1C204D6}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{6784AAA4-02B8-40E5-94DA-04D0ABA2C492} = {7932220C-0DD8-45A2-AF56-AA40D34683C6}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E48C0497-998F-42FF-B341-3CC083278048}
	EndGlobalSection
EndGlobal
