﻿namespace FileStorageHub.Services.Helpers
{
    using FileStorageHub.Domain.ViewModels;

    public class ResponseHelper
    {
        private readonly string _urlTemplate;
        private readonly int _page;
        private readonly int _pageSize;
        private readonly int _resCount;
        private readonly int _totalCount;

        public ResponseHelper(string urlTemplate, int page, int pageSize, int resCount, int totalCount)
        {
            _urlTemplate = urlTemplate;
            _page = page;
            _pageSize = pageSize;
            _resCount = resCount;
            _totalCount = totalCount;
        }
        public MetaDataVM GetMetaData()
        {
            var paginationLinks = GetPaginationLinks();

            return new MetaDataVM
            {
                Page = _page,
                Page_count = _resCount,
                Total_count = _totalCount,
                Per_page = _pageSize,
                Links = paginationLinks,
            };
        }

        private LinkVM GetPaginationLinks()
        {
            int lastPageNumber = (int)Math.Ceiling((double)_totalCount / _pageSize);

            var first = _page != 1 ? string.Format(_urlTemplate, 1, _pageSize) : null;
            var previous = _page > 1 ? string.Format(_urlTemplate, _page - 1, _pageSize) : null;
            var next = _page != lastPageNumber ? string.Format(_urlTemplate, _page + 1, _pageSize) : null;
            var last = _page != lastPageNumber ? string.Format(_urlTemplate, lastPageNumber, _pageSize) : null;

            return new Domain.ViewModels.LinkVM
            {
                Self = string.Format(_urlTemplate, _page, _pageSize),
                First = first,
                Previous = previous,
                Next = next,
                Last = last
            };
        }
    }
}
