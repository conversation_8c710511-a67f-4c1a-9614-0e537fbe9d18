﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <!--<Nullable>enable</Nullable>-->
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.20" />
    <PackageReference Include="NLog" Version="5.2.3" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BranchedProjects\Logging\Logging.csproj" />
    <ProjectReference Include="..\FileStorageHub.Domain\FileStorageHub.Domain.csproj" />
    <ProjectReference Include="..\FileStorageHub.Persistence\FileStorageHub.Persistence.csproj" />
    <ProjectReference Include="..\FileStorageHub.Services\FileStorageHub.Services.csproj" />
    <ProjectReference Include="..\LoggerService\LoggerService.csproj" />
  </ItemGroup>

</Project>
